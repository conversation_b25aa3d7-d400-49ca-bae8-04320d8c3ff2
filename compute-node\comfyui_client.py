#!/usr/bin/env python3
"""
ComfyUI云端算力客户端
连接到远程ComfyUI服务进行图像生成
"""

import asyncio
import aiohttp
import json
import logging
import time
import uuid
import websockets
import base64
from typing import Dict, Any, Optional, List
from pathlib import Path
import yaml

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ComfyUIClient:
    """ComfyUI客户端"""
    
    def __init__(self, server_url: str = "https://koomfonjr8-8188.cnb.run"):
        self.server_url = server_url.rstrip('/')
        self.ws_url = server_url.replace('https://', 'wss://').replace('http://', 'ws://')
        self.client_id = str(uuid.uuid4())
        self.session: Optional[aiohttp.ClientSession] = None
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        
    async def connect(self):
        """连接到ComfyUI服务"""
        try:
            # 创建HTTP会话
            self.session = aiohttp.ClientSession()
            
            # 测试连接
            async with self.session.get(f"{self.server_url}/") as response:
                if response.status == 200:
                    logger.info(f"成功连接到ComfyUI服务: {self.server_url}")
                else:
                    raise Exception(f"连接失败，状态码: {response.status}")
            
            # 连接WebSocket
            ws_url = f"{self.ws_url}/ws?clientId={self.client_id}"
            self.websocket = await websockets.connect(ws_url)
            logger.info("WebSocket连接成功")
            
        except Exception as e:
            logger.error(f"连接失败: {e}")
            raise
    
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
        if self.session:
            await self.session.close()
        logger.info("连接已断开")
    
    async def get_models(self) -> Dict[str, List[str]]:
        """获取可用模型列表"""
        try:
            async with self.session.get(f"{self.server_url}/object_info") as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 提取检查点模型
                    checkpoints = []
                    if "CheckpointLoaderSimple" in data:
                        inputs = data["CheckpointLoaderSimple"]["input"]
                        if "ckpt_name" in inputs:
                            checkpoints = inputs["ckpt_name"][0]
                    
                    # 提取VAE模型
                    vaes = []
                    if "VAELoader" in data:
                        inputs = data["VAELoader"]["input"]
                        if "vae_name" in inputs:
                            vaes = inputs["vae_name"][0]
                    
                    return {
                        "checkpoints": checkpoints,
                        "vaes": vaes
                    }
                else:
                    raise Exception(f"获取模型列表失败: {response.status}")
                    
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            return {"checkpoints": [], "vaes": []}
    
    def create_workflow(self, 
                       prompt: str,
                       negative_prompt: str = "",
                       width: int = 1024,
                       height: int = 1024,
                       steps: int = 30,
                       cfg_scale: float = 7.5,
                       seed: int = -1,
                       checkpoint: str = "sd_xl_base_1.0.safetensors") -> Dict[str, Any]:
        """创建ComfyUI工作流"""
        
        if seed == -1:
            seed = int(time.time() * 1000) % 2147483647
        
        workflow = {
            "3": {
                "inputs": {
                    "seed": seed,
                    "steps": steps,
                    "cfg": cfg_scale,
                    "sampler_name": "euler",
                    "scheduler": "normal",
                    "denoise": 1,
                    "model": ["4", 0],
                    "positive": ["6", 0],
                    "negative": ["7", 0],
                    "latent_image": ["5", 0]
                },
                "class_type": "KSampler",
                "_meta": {"title": "KSampler"}
            },
            "4": {
                "inputs": {
                    "ckpt_name": checkpoint
                },
                "class_type": "CheckpointLoaderSimple",
                "_meta": {"title": "Load Checkpoint"}
            },
            "5": {
                "inputs": {
                    "width": width,
                    "height": height,
                    "batch_size": 1
                },
                "class_type": "EmptyLatentImage",
                "_meta": {"title": "Empty Latent Image"}
            },
            "6": {
                "inputs": {
                    "text": prompt,
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Prompt)"}
            },
            "7": {
                "inputs": {
                    "text": negative_prompt,
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Negative)"}
            },
            "8": {
                "inputs": {
                    "samples": ["3", 0],
                    "vae": ["4", 2]
                },
                "class_type": "VAEDecode",
                "_meta": {"title": "VAE Decode"}
            },
            "9": {
                "inputs": {
                    "filename_prefix": "ComfyUI",
                    "images": ["8", 0]
                },
                "class_type": "SaveImage",
                "_meta": {"title": "Save Image"}
            }
        }
        
        return workflow
    
    async def queue_prompt(self, workflow: Dict[str, Any]) -> str:
        """提交工作流到队列"""
        try:
            prompt_data = {
                "prompt": workflow,
                "client_id": self.client_id
            }
            
            async with self.session.post(
                f"{self.server_url}/prompt",
                json=prompt_data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    prompt_id = result["prompt_id"]
                    logger.info(f"工作流已提交，ID: {prompt_id}")
                    return prompt_id
                else:
                    error_text = await response.text()
                    raise Exception(f"提交工作流失败: {response.status} - {error_text}")
                    
        except Exception as e:
            logger.error(f"提交工作流失败: {e}")
            raise
    
    async def wait_for_completion(self, prompt_id: str, timeout: int = 300) -> Dict[str, Any]:
        """等待任务完成"""
        start_time = time.time()
        
        try:
            while time.time() - start_time < timeout:
                # 监听WebSocket消息
                try:
                    message = await asyncio.wait_for(
                        self.websocket.recv(), 
                        timeout=1.0
                    )
                    
                    data = json.loads(message)
                    
                    if data["type"] == "executing":
                        if data["data"]["node"] is None and data["data"]["prompt_id"] == prompt_id:
                            logger.info(f"任务 {prompt_id} 执行完成")
                            break
                    
                    elif data["type"] == "progress":
                        progress = data["data"]
                        logger.info(f"进度: {progress['value']}/{progress['max']}")
                    
                    elif data["type"] == "execution_error":
                        error_info = data["data"]
                        raise Exception(f"执行错误: {error_info}")
                        
                except asyncio.TimeoutError:
                    continue
                except Exception as e:
                    logger.error(f"WebSocket错误: {e}")
                    break
            
            # 获取生成的图像
            return await self.get_images(prompt_id)
            
        except Exception as e:
            logger.error(f"等待任务完成失败: {e}")
            raise
    
    async def get_images(self, prompt_id: str) -> Dict[str, Any]:
        """获取生成的图像"""
        try:
            async with self.session.get(f"{self.server_url}/history/{prompt_id}") as response:
                if response.status == 200:
                    history = await response.json()
                    
                    if prompt_id not in history:
                        raise Exception("任务历史记录不存在")
                    
                    outputs = history[prompt_id]["outputs"]
                    images = []
                    
                    # 查找保存的图像
                    for node_id, output in outputs.items():
                        if "images" in output:
                            for image_info in output["images"]:
                                # 下载图像
                                image_url = f"{self.server_url}/view"
                                params = {
                                    "filename": image_info["filename"],
                                    "subfolder": image_info["subfolder"],
                                    "type": image_info["type"]
                                }
                                
                                async with self.session.get(image_url, params=params) as img_response:
                                    if img_response.status == 200:
                                        image_data = await img_response.read()
                                        image_base64 = base64.b64encode(image_data).decode()
                                        
                                        images.append({
                                            "filename": image_info["filename"],
                                            "data": image_base64,
                                            "format": "png"
                                        })
                    
                    return {
                        "prompt_id": prompt_id,
                        "images": images,
                        "status": "completed"
                    }
                else:
                    raise Exception(f"获取历史记录失败: {response.status}")
                    
        except Exception as e:
            logger.error(f"获取图像失败: {e}")
            raise
    
    async def generate_image(self,
                           prompt: str,
                           negative_prompt: str = "",
                           width: int = 1024,
                           height: int = 1024,
                           steps: int = 30,
                           cfg_scale: float = 7.5,
                           seed: int = -1,
                           checkpoint: str = None) -> Dict[str, Any]:
        """生成图像的完整流程"""
        try:
            # 获取可用模型
            if checkpoint is None:
                models = await self.get_models()
                if models["checkpoints"]:
                    checkpoint = models["checkpoints"][0]
                else:
                    raise Exception("没有可用的检查点模型")
            
            logger.info(f"使用模型: {checkpoint}")
            logger.info(f"生成参数: {width}x{height}, {steps}步, CFG={cfg_scale}")
            
            # 创建工作流
            workflow = self.create_workflow(
                prompt=prompt,
                negative_prompt=negative_prompt,
                width=width,
                height=height,
                steps=steps,
                cfg_scale=cfg_scale,
                seed=seed,
                checkpoint=checkpoint
            )
            
            # 提交任务
            prompt_id = await self.queue_prompt(workflow)
            
            # 等待完成
            result = await self.wait_for_completion(prompt_id)
            
            logger.info(f"图像生成完成，共 {len(result['images'])} 张图片")
            return result
            
        except Exception as e:
            logger.error(f"图像生成失败: {e}")
            raise

async def main():
    """测试函数"""
    client = ComfyUIClient("https://koomfonjr8-8188.cnb.run")
    
    try:
        # 连接服务
        await client.connect()
        
        # 获取可用模型
        models = await client.get_models()
        print("可用模型:")
        for model_type, model_list in models.items():
            print(f"  {model_type}: {model_list[:3]}...")  # 只显示前3个
        
        # 生成测试图像
        result = await client.generate_image(
            prompt="a beautiful landscape with mountains and lake, sunset, highly detailed",
            negative_prompt="blurry, low quality",
            width=1024,
            height=1024,
            steps=20,
            cfg_scale=7.5
        )
        
        print(f"生成完成！共 {len(result['images'])} 张图片")
        
        # 保存图片到本地（可选）
        for i, image in enumerate(result['images']):
            image_data = base64.b64decode(image['data'])
            with open(f"generated_image_{i}.png", "wb") as f:
                f.write(image_data)
            print(f"图片已保存: generated_image_{i}.png")
        
    except Exception as e:
        print(f"错误: {e}")
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())

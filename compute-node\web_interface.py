#!/usr/bin/env python3
"""
ComfyUI算力节点Web管理界面
提供简单的Web界面来管理和监控算力节点
"""

import asyncio
import json
import time
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse
import threading

# 全局状态
node_status = {
    "status": "offline",
    "start_time": None,
    "total_tasks": 0,
    "active_tasks": 0,
    "last_update": None,
    "comfyui_url": "https://koomfonjr8-8188.cnb.run",
    "errors": []
}

class WebHandler(BaseHTTPRequestHandler):
    """Web请求处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == "/" or self.path == "/index.html":
            self.serve_dashboard()
        elif self.path == "/api/status":
            self.serve_status_api()
        elif self.path == "/api/test":
            self.serve_test_api()
        elif self.path.startswith("/static/"):
            self.serve_static()
        else:
            self.send_error(404)
    
    def do_POST(self):
        """处理POST请求"""
        if self.path == "/api/start":
            self.handle_start_node()
        elif self.path == "/api/stop":
            self.handle_stop_node()
        elif self.path == "/api/test_comfyui":
            self.handle_test_comfyui()
        else:
            self.send_error(404)
    
    def serve_dashboard(self):
        """提供仪表板页面"""
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComfyUI算力节点管理</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .content {{
            padding: 30px;
        }}
        .status-card {{
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
        }}
        .status-offline {{
            border-left-color: #dc3545;
        }}
        .status-online {{
            border-left-color: #28a745;
        }}
        .status-busy {{
            border-left-color: #ffc107;
        }}
        .grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        .card {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .btn {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: transform 0.2s;
        }}
        .btn:hover {{
            transform: translateY(-2px);
        }}
        .btn-success {{
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }}
        .btn-danger {{
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        }}
        .btn-info {{
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }}
        .log {{
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }}
        .metric {{
            text-align: center;
            padding: 10px;
        }}
        .metric-value {{
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }}
        .metric-label {{
            color: #666;
            font-size: 0.9em;
        }}
        .refresh {{
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }}
    </style>
</head>
<body>
    <button class="btn btn-info refresh" onclick="refreshStatus()">🔄 刷新</button>
    
    <div class="container">
        <div class="header">
            <h1>🎨 ComfyUI算力节点管理</h1>
            <p>云端AI绘画算力节点监控与管理平台</p>
        </div>
        
        <div class="content">
            <!-- 状态卡片 -->
            <div id="statusCard" class="status-card status-offline">
                <h3>📊 节点状态</h3>
                <p id="statusText">节点离线</p>
                <p id="statusTime">最后更新: 从未</p>
            </div>
            
            <!-- 控制按钮 -->
            <div style="text-align: center; margin: 30px 0;">
                <button class="btn btn-info" onclick="testComfyUI()">🔍 测试ComfyUI连接</button>
                <button class="btn btn-success" onclick="startNode()">🚀 启动节点</button>
                <button class="btn btn-danger" onclick="stopNode()">⏹️ 停止节点</button>
            </div>
            
            <!-- 指标网格 -->
            <div class="grid">
                <div class="card">
                    <div class="metric">
                        <div class="metric-value" id="totalTasks">0</div>
                        <div class="metric-label">总任务数</div>
                    </div>
                </div>
                <div class="card">
                    <div class="metric">
                        <div class="metric-value" id="activeTasks">0</div>
                        <div class="metric-label">活跃任务</div>
                    </div>
                </div>
                <div class="card">
                    <div class="metric">
                        <div class="metric-value" id="uptime">0分钟</div>
                        <div class="metric-label">运行时间</div>
                    </div>
                </div>
            </div>
            
            <!-- ComfyUI信息 -->
            <div class="card">
                <h3>🔗 ComfyUI服务</h3>
                <p><strong>地址:</strong> <a href="{node_status['comfyui_url']}" target="_blank">{node_status['comfyui_url']}</a></p>
                <p><strong>状态:</strong> <span id="comfyuiStatus">未知</span></p>
                <button class="btn btn-info" onclick="openComfyUI()">🌐 打开ComfyUI界面</button>
            </div>
            
            <!-- 日志 -->
            <div class="card">
                <h3>📝 系统日志</h3>
                <div id="logContainer" class="log">
                    等待日志信息...
                </div>
            </div>
        </div>
    </div>

    <script>
        // 刷新状态
        function refreshStatus() {{
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {{
                    updateUI(data);
                }})
                .catch(error => {{
                    console.error('Error:', error);
                    addLog('❌ 获取状态失败: ' + error.message);
                }});
        }}
        
        // 更新UI
        function updateUI(status) {{
            const statusCard = document.getElementById('statusCard');
            const statusText = document.getElementById('statusText');
            const statusTime = document.getElementById('statusTime');
            
            // 更新状态
            statusCard.className = 'status-card status-' + status.status;
            statusText.textContent = getStatusText(status.status);
            statusTime.textContent = '最后更新: ' + new Date().toLocaleString();
            
            // 更新指标
            document.getElementById('totalTasks').textContent = status.total_tasks;
            document.getElementById('activeTasks').textContent = status.active_tasks;
            document.getElementById('uptime').textContent = calculateUptime(status.start_time);
            
            // 更新日志
            if (status.errors && status.errors.length > 0) {{
                const logContainer = document.getElementById('logContainer');
                logContainer.innerHTML = status.errors.slice(-10).map(error => 
                    '<div>' + new Date().toLocaleTimeString() + ' ' + error + '</div>'
                ).join('');
            }}
        }}
        
        // 获取状态文本
        function getStatusText(status) {{
            switch(status) {{
                case 'online': return '✅ 节点在线';
                case 'busy': return '🔄 节点忙碌';
                case 'offline': return '❌ 节点离线';
                default: return '❓ 状态未知';
            }}
        }}
        
        // 计算运行时间
        function calculateUptime(startTime) {{
            if (!startTime) return '0分钟';
            const now = new Date();
            const start = new Date(startTime);
            const diff = Math.floor((now - start) / 1000 / 60);
            return diff + '分钟';
        }}
        
        // 添加日志
        function addLog(message) {{
            const logContainer = document.getElementById('logContainer');
            const time = new Date().toLocaleTimeString();
            logContainer.innerHTML += '<div>' + time + ' ' + message + '</div>';
            logContainer.scrollTop = logContainer.scrollHeight;
        }}
        
        // 测试ComfyUI
        function testComfyUI() {{
            addLog('🔍 开始测试ComfyUI连接...');
            fetch('/api/test_comfyui', {{method: 'POST'}})
                .then(response => response.json())
                .then(data => {{
                    if (data.success) {{
                        addLog('✅ ComfyUI连接测试成功');
                        document.getElementById('comfyuiStatus').textContent = '✅ 正常';
                    }} else {{
                        addLog('❌ ComfyUI连接测试失败: ' + data.error);
                        document.getElementById('comfyuiStatus').textContent = '❌ 异常';
                    }}
                }})
                .catch(error => {{
                    addLog('❌ 测试请求失败: ' + error.message);
                }});
        }}
        
        // 启动节点
        function startNode() {{
            addLog('🚀 启动算力节点...');
            fetch('/api/start', {{method: 'POST'}})
                .then(response => response.json())
                .then(data => {{
                    if (data.success) {{
                        addLog('✅ 节点启动成功');
                    }} else {{
                        addLog('❌ 节点启动失败: ' + data.error);
                    }}
                    refreshStatus();
                }})
                .catch(error => {{
                    addLog('❌ 启动请求失败: ' + error.message);
                }});
        }}
        
        // 停止节点
        function stopNode() {{
            addLog('⏹️ 停止算力节点...');
            fetch('/api/stop', {{method: 'POST'}})
                .then(response => response.json())
                .then(data => {{
                    if (data.success) {{
                        addLog('✅ 节点停止成功');
                    }} else {{
                        addLog('❌ 节点停止失败: ' + data.error);
                    }}
                    refreshStatus();
                }})
                .catch(error => {{
                    addLog('❌ 停止请求失败: ' + error.message);
                }});
        }}
        
        // 打开ComfyUI界面
        function openComfyUI() {{
            window.open('{node_status['comfyui_url']}', '_blank');
        }}
        
        // 页面加载完成后自动刷新状态
        document.addEventListener('DOMContentLoaded', function() {{
            refreshStatus();
            // 每30秒自动刷新
            setInterval(refreshStatus, 30000);
        }});
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def serve_status_api(self):
        """提供状态API"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        
        status = node_status.copy()
        status['last_update'] = datetime.now().isoformat()
        
        self.wfile.write(json.dumps(status, ensure_ascii=False).encode('utf-8'))
    
    def handle_test_comfyui(self):
        """处理ComfyUI测试请求"""
        try:
            # 这里可以调用实际的测试函数
            import urllib.request
            with urllib.request.urlopen(node_status['comfyui_url'], timeout=5) as response:
                if response.getcode() == 200:
                    result = {"success": True, "message": "连接成功"}
                else:
                    result = {"success": False, "error": f"状态码: {response.getcode()}"}
        except Exception as e:
            result = {"success": False, "error": str(e)}
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(result, ensure_ascii=False).encode('utf-8'))
    
    def handle_start_node(self):
        """处理启动节点请求"""
        try:
            node_status['status'] = 'online'
            node_status['start_time'] = datetime.now().isoformat()
            result = {"success": True, "message": "节点启动成功"}
        except Exception as e:
            result = {"success": False, "error": str(e)}
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(result, ensure_ascii=False).encode('utf-8'))
    
    def handle_stop_node(self):
        """处理停止节点请求"""
        try:
            node_status['status'] = 'offline'
            node_status['start_time'] = None
            result = {"success": True, "message": "节点停止成功"}
        except Exception as e:
            result = {"success": False, "error": str(e)}
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(result, ensure_ascii=False).encode('utf-8'))
    
    def log_message(self, format, *args):
        """禁用默认日志输出"""
        pass

def start_web_server(port=8080):
    """启动Web服务器"""
    server = HTTPServer(('localhost', port), WebHandler)
    print(f"🌐 Web管理界面启动成功!")
    print(f"📱 访问地址: http://localhost:{port}")
    print(f"🎨 ComfyUI地址: {node_status['comfyui_url']}")
    print("按 Ctrl+C 停止服务")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Web服务器已停止")
        server.shutdown()

if __name__ == "__main__":
    start_web_server()
